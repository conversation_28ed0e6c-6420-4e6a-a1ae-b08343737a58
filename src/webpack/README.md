# Webpack v5 Codemod 迁移器

基于官方 webpack v5 codemod 实现的代码迁移工具，提供 API 方式的代码转换，支持没有 AI 的场景。

## 🎯 功能特性

### 支持的转换

1. **migrate-library-target-to-library-object**
   - 将 webpack 4 的 `output.library` 和 `output.libraryTarget` 配置转换为 webpack 5 的 `output.library` 对象格式

2. **set-target-to-false-and-update-plugins**
   - 将 webpack 配置中的 `target` 函数调用转换为 `false`，并将原函数调用移动到 `plugins` 数组中

3. **json-imports-to-default-imports**
   - 将 JSON 文件的命名导入转换为默认导入

### 核心优势

- ✅ **无需 AI**：基于确定性的代码转换规则，不依赖 AI 服务
- ✅ **官方标准**：基于 webpack 官方 codemod 实现
- ✅ **API 调用**：提供编程接口，可集成到现有工具链
- ✅ **智能检测**：自动检测项目是否需要迁移
- ✅ **安全可靠**：支持预览模式和自动备份

## 🚀 使用方法

### CLI 命令行使用

```bash
# 检测项目是否需要迁移
webpack-codemod detect [project-path]

# 执行迁移
webpack-codemod migrate [project-path]

# 预览模式（不实际修改文件）
webpack-codemod migrate [project-path] --dry-run

# 显示详细信息
webpack-codemod migrate [project-path] --verbose
```

### API 编程使用

```javascript
const WebpackCodemodMigrator = require('./src/webpack/WebpackCodemodMigrator');
const WebpackConfigDetector = require('./src/webpack/utils/webpackConfigDetector');

// 检测项目
const detector = new WebpackConfigDetector('/path/to/project');
const strategy = await detector.getRecommendedStrategy();

if (strategy.shouldMigrate) {
  // 执行迁移
  const migrator = new WebpackCodemodMigrator('/path/to/project', {
    dryRun: false,
    verbose: true,
    backup: true
  });
  
  const result = await migrator.migrate();
  console.log(`迁移完成，修改了 ${result.stats.modifiedFiles} 个文件`);
}
```

### 集成到 BuildFixer

WebpackCodemodMigrator 已集成到 BuildFixer 中作为备选修复方案：

```javascript
const BuildFixer = require('./src/build/buildFixer');

const fixer = new BuildFixer('/path/to/project');
const result = await fixer.fix(); // 会自动尝试 webpack codemod 迁移
```

## 📋 转换示例

### 1. Library Target 转换

**转换前 (webpack 4):**
```javascript
module.exports = {
  output: {
    library: 'MyLibrary',
    libraryTarget: 'commonjs2'
  }
};
```

**转换后 (webpack 5):**
```javascript
module.exports = {
  output: {
    library: {
      name: 'MyLibrary',
      type: 'commonjs2',
    },
  },
};
```

### 2. Target Function 转换

**转换前 (webpack 4):**
```javascript
module.exports = {
  target: WebExtensionTarget(nodeConfig)
};
```

**转换后 (webpack 5):**
```javascript
module.exports = {
  target: false,
  plugins: [
    WebExtensionTarget(nodeConfig)
  ]
};
```

### 3. JSON Imports 转换

**转换前 (webpack 4):**
```javascript
import { version, name } from './package.json';
console.log(name, version);
```

**转换后 (webpack 5):**
```javascript
import pkg from './package.json';
console.log(pkg.name, pkg.version);
```

## 🔧 配置选项

### WebpackCodemodMigrator 选项

```javascript
const options = {
  dryRun: false,           // 预览模式，不实际修改文件
  verbose: false,          // 显示详细信息
  backup: true,            // 创建备份文件
  include: [               // 包含的文件模式
    '**/*.js',
    '**/*.jsx',
    '**/*.ts',
    '**/*.tsx'
  ],
  exclude: [               // 排除的文件模式
    'node_modules/**',
    'dist/**',
    'build/**',
    '**/*.min.js'
  ],
  parser: 'tsx'            // jscodeshift 解析器
};
```

### CLI 选项

```bash
Options:
  --dry-run                    预览模式，不实际修改文件
  --no-backup                  不创建备份文件
  --verbose                    显示详细信息
  --include <patterns...>      包含的文件模式
  --exclude <patterns...>      排除的文件模式
  --parser <parser>            jscodeshift 解析器 (default: "tsx")
  -h, --help                   显示帮助信息
```

## 🧪 测试

```bash
# 运行 webpack codemod 相关测试
npm test -- test/webpack/

# 运行特定测试文件
npm test test/webpack/webpackCodemodMigrator.test.js
```

## 📊 检测和分析

### 项目检测

WebpackConfigDetector 可以检测：

- ✅ 项目是否使用 webpack
- ✅ webpack 版本信息
- ✅ 配置文件位置
- ✅ 相关工具和插件
- ✅ 需要迁移的代码模式

### 迁移策略

基于检测结果，系统会提供：

- 🎯 是否需要迁移的建议
- 📊 优先级评估
- 📝 具体的迁移建议
- 📈 预估影响的文件数量

## 🔗 与现有工具集成

### 在 AutoMigrator 中使用

```javascript
const AutoMigrator = require('./src/AutoMigrator');

// WebpackCodemodMigrator 可以作为迁移步骤之一
const migrator = new AutoMigrator(projectPath);
await migrator.migrate(); // 会自动包含 webpack codemod 迁移
```

### 在 BuildFixer 中使用

BuildFixer 会在 AI 修复之前先尝试 webpack codemod 迁移：

```javascript
const BuildFixer = require('./src/build/buildFixer');

const fixer = new BuildFixer(projectPath, {
  skipAI: false  // 即使启用 AI，也会先尝试 codemod 迁移
});

const result = await fixer.fix();
```

## 🛡️ 安全性

- **备份机制**：默认为每个修改的文件创建 `.backup` 备份
- **预览模式**：支持 `--dry-run` 模式预览更改
- **错误处理**：完善的错误处理和回滚机制
- **文件验证**：修改前验证文件语法正确性

## 📚 参考资料

- [Webpack v5 迁移指南](https://webpack.js.org/migrate/5/)
- [官方 Codemod 实现](https://github.com/codemod-com/commons/tree/main/codemods/webpack/v5)
- [jscodeshift 文档](https://github.com/facebook/jscodeshift)

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个工具！
