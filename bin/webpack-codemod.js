#!/usr/bin/env node

const { Command } = require('commander');
const chalk = require('chalk');
const path = require('path');
const fs = require('fs-extra');
const ora = require('ora');
const WebpackCodemodMigrator = require('../src/webpack/WebpackCodemodMigrator');
const WebpackConfigDetector = require('../src/webpack/utils/webpackConfigDetector');

const program = new Command();

program
  .name('webpack-codemod')
  .description('Webpack v5 代码迁移工具 - 基于官方 codemod 实现')
  .version('1.0.0');

program
  .command('migrate [project-path]')
  .description('🔧 执行 webpack v5 代码迁移')
  .option('--dry-run', '预览模式，不实际修改文件')
  .option('--no-backup', '不创建备份文件')
  .option('--verbose', '显示详细信息')
  .option('--include <patterns...>', '包含的文件模式', ['**/*.js', '**/*.jsx', '**/*.ts', '**/*.tsx'])
  .option('--exclude <patterns...>', '排除的文件模式', ['node_modules/**', 'dist/**', 'build/**'])
  .option('--parser <parser>', 'jscodeshift 解析器', 'tsx')
  .action(async (projectPath, options) => {
    try {
      const targetPath = path.resolve(projectPath || process.cwd());
      
      console.log(chalk.bold.blue('\n🔧 Webpack v5 Codemod 迁移工具'));
      console.log(chalk.gray(`项目路径: ${targetPath}`));

      // 检查项目路径是否存在
      if (!await fs.pathExists(targetPath)) {
        console.error(chalk.red('❌ 项目路径不存在'));
        process.exit(1);
      }

      // 预检查
      const spinner = ora('检测项目配置...').start();
      const detector = new WebpackConfigDetector(targetPath);
      const webpackInfo = await detector.detectWebpack();
      const strategy = await detector.getRecommendedStrategy();
      spinner.stop();

      // 显示检测结果
      console.log(chalk.bold.blue('\n📋 项目分析结果'));
      console.log(chalk.gray(`Webpack 检测: ${webpackInfo.hasWebpack ? '✅ 已安装' : '❌ 未检测到'}`));
      
      if (webpackInfo.hasWebpack) {
        console.log(chalk.gray(`Webpack 版本: ${webpackInfo.version || '未知'}`));
        console.log(chalk.gray(`配置文件数量: ${webpackInfo.configFiles.length}`));
        
        if (webpackInfo.configFiles.length > 0) {
          console.log(chalk.gray('配置文件:'));
          webpackInfo.configFiles.forEach(file => {
            console.log(chalk.gray(`  - ${path.relative(targetPath, file)}`));
          });
        }
      }

      console.log(chalk.bold.blue('\n🎯 迁移策略'));
      console.log(chalk.gray(`是否需要迁移: ${strategy.shouldMigrate ? '✅ 是' : '❌ 否'}`));
      console.log(chalk.gray(`优先级: ${strategy.priority}`));
      console.log(chalk.gray(`预估影响文件: ${strategy.estimatedFiles}`));
      
      if (strategy.recommendations.length > 0) {
        console.log(chalk.gray('建议:'));
        strategy.recommendations.forEach(rec => {
          console.log(chalk.gray(`  - ${rec}`));
        });
      }

      if (!strategy.shouldMigrate) {
        console.log(chalk.yellow('\n⚠️  根据分析结果，当前项目可能不需要迁移'));
        console.log(chalk.gray('如果您确定要继续，请使用 --force 选项'));
        return;
      }

      // 执行迁移
      console.log(chalk.bold.blue('\n🚀 开始迁移'));
      
      const migrator = new WebpackCodemodMigrator(targetPath, {
        dryRun: options.dryRun,
        verbose: options.verbose,
        backup: options.backup,
        include: options.include,
        exclude: options.exclude,
        parser: options.parser
      });

      const result = await migrator.migrate();

      if (result.success) {
        console.log(chalk.bold.green('\n✅ 迁移完成'));
        
        if (options.dryRun) {
          console.log(chalk.yellow('🔍 预览模式 - 未实际修改文件'));
        }
        
        console.log(chalk.gray(`处理文件: ${result.stats.processedFiles}`));
        console.log(chalk.gray(`修改文件: ${result.stats.modifiedFiles}`));
        console.log(chalk.gray(`跳过文件: ${result.stats.skippedFiles}`));
        
        if (result.stats.errors.length > 0) {
          console.log(chalk.yellow(`\n⚠️  处理过程中遇到 ${result.stats.errors.length} 个错误:`));
          result.stats.errors.forEach(error => {
            console.log(chalk.red(`  ${path.relative(targetPath, error.file)}: ${error.error}`));
          });
        }

        if (!options.dryRun && result.stats.modifiedFiles > 0) {
          console.log(chalk.bold.blue('\n📝 后续步骤'));
          console.log(chalk.gray('1. 检查修改的文件，确保转换正确'));
          console.log(chalk.gray('2. 运行测试确保功能正常'));
          console.log(chalk.gray('3. 如有问题，可使用备份文件恢复'));
        }

      } else {
        console.error(chalk.red('\n❌ 迁移失败:'), result.message);
        process.exit(1);
      }

    } catch (error) {
      console.error(chalk.red('❌ 执行失败:'), error.message);
      if (options.verbose) {
        console.error(error.stack);
      }
      process.exit(1);
    }
  });

program
  .command('detect [project-path]')
  .description('🔍 检测项目是否需要 webpack v5 迁移')
  .option('--verbose', '显示详细信息')
  .action(async (projectPath, options) => {
    try {
      const targetPath = path.resolve(projectPath || process.cwd());
      
      console.log(chalk.bold.blue('\n🔍 Webpack 项目检测'));
      console.log(chalk.gray(`项目路径: ${targetPath}`));

      const detector = new WebpackConfigDetector(targetPath);
      const webpackInfo = await detector.detectWebpack();
      const migrationNeeds = await detector.detectMigrationNeeds();
      const strategy = await detector.getRecommendedStrategy();

      console.log(chalk.bold.blue('\n📊 检测结果'));
      console.log(chalk.gray(`Webpack: ${webpackInfo.hasWebpack ? '✅ 检测到' : '❌ 未检测到'}`));
      
      if (webpackInfo.hasWebpack) {
        console.log(chalk.gray(`版本: ${webpackInfo.version || '未知'}`));
        console.log(chalk.gray(`配置文件: ${webpackInfo.configFiles.length} 个`));
        
        if (options.verbose && webpackInfo.configFiles.length > 0) {
          webpackInfo.configFiles.forEach(file => {
            console.log(chalk.gray(`  - ${path.relative(targetPath, file)}`));
          });
        }

        console.log(chalk.bold.blue('\n🔄 迁移需求分析'));
        console.log(chalk.gray(`Library Target 转换: ${migrationNeeds.libraryTarget ? '✅ 需要' : '❌ 不需要'}`));
        console.log(chalk.gray(`Target Function 转换: ${migrationNeeds.targetFunction ? '✅ 需要' : '❌ 不需要'}`));
        console.log(chalk.gray(`JSON Imports 转换: ${migrationNeeds.jsonImports ? '✅ 需要' : '❌ 不需要'}`));
        console.log(chalk.gray(`影响文件数: ${migrationNeeds.files.length}`));

        if (options.verbose && migrationNeeds.files.length > 0) {
          console.log(chalk.gray('\n需要处理的文件:'));
          migrationNeeds.files.forEach(file => {
            const relativePath = path.relative(targetPath, file.path);
            const needs = [];
            if (file.hasLibraryTarget) needs.push('LibraryTarget');
            if (file.hasTargetFunction) needs.push('TargetFunction');
            if (file.hasJsonImports) needs.push('JsonImports');
            console.log(chalk.gray(`  - ${relativePath} (${needs.join(', ')})`));
          });
        }

        console.log(chalk.bold.blue('\n🎯 推荐策略'));
        console.log(chalk.gray(`是否需要迁移: ${strategy.shouldMigrate ? '✅ 是' : '❌ 否'}`));
        console.log(chalk.gray(`优先级: ${strategy.priority}`));
        
        strategy.recommendations.forEach(rec => {
          console.log(chalk.gray(`- ${rec}`));
        });

        if (strategy.shouldMigrate) {
          console.log(chalk.bold.green('\n💡 建议执行迁移命令:'));
          console.log(chalk.green(`  webpack-codemod migrate ${targetPath}`));
        }
      }

    } catch (error) {
      console.error(chalk.red('❌ 检测失败:'), error.message);
      if (options.verbose) {
        console.error(error.stack);
      }
      process.exit(1);
    }
  });

program.parse();
